<template>
    <SectionCard
        class="left-section-card"
        :title="level === 'province' ? '全省实时软采流量' : '全市实时软采流量'"
        titleBg="title-bg1"
    >
        <div class="real-time-flow" v-if="level === 'province'">
            <div class="network-flow">
                <div
                    class="flow-item"
                    :class="{ active: activeType === item.type }"
                    v-for="item in btnGroup"
                    :key="item.label"
                    @click="handleClick(item.type)"
                >
                    <span class="flow-label">{{ item.label }}</span>
                    <span class="flow-value">{{ item.value }}<span>亿条</span></span>
                </div>
            </div>
            <div class="flow-chart" ref="flowChart"></div>
        </div>
        <div class="real-time-flow city-flow" v-else>
            <div v-for="(item, key) in cityData" :key="key" class="flow-city-item">
                <div class="flow-city-header">
                    <span class="flow-label">{{ item.label }}</span>
                    <span class="flow-value">{{ item.value }}<span>亿条</span></span>
                </div>
                <div class="flow-chart" :id="'flowChart-' + key" :ref="'flowChart-' + key"></div>
            </div>
        </div>
    </SectionCard>
</template>

<script>
import * as echarts from 'echarts';
import dayjs from 'dayjs';
import SectionCard from './SectionCard.vue';
import commonMixins from '@/script/mixins/commonMixins.js';

const chartsOption = {
    color: ['#008BFF', '#FFC602'],
    grid: {
        left: '2%',
        right: 0,
        bottom: 0,
        top: '25%',
        containLabel: true
    },
    tooltip: {
        trigger: 'axis',
        backgroundColor: '#09437B',
        borderColor: '#2D72B6',
        borderWidth: 1,
        padding: [5, 10],
        textStyle: {
            color: '#fff',
            fontSize: 12
        }
    },
    xAxis: {
        type: 'category',
        boundaryGap: true,
        data: [],
        axisLine: {
            lineStyle: {
                color: 'rgba(255, 255, 255, 0.2)'
            }
        },
        axisLabel: {
            color: 'rgba(255, 255, 255, 0.6)',
            fontSize: 10,
            formatter: (value) => {
                return value.slice(11, 16); // 只显示时:分
            }
        },
        splitLine: {
            show: true,
            interval: 0,
            lineStyle: {
                type: 'dashed',
                color: 'rgba(255, 255, 255, 0.1)'
            }
        },
        axisTick: {
            show: false,
            alignWithLabel: true
        }
    },
    yAxis: {
        type: 'value',
        name: '万条',
        nameTextStyle: {
            color: 'rgba(255, 255, 255, 0.6)',
            fontSize: 10
        },
        axisLine: {
            lineStyle: {
                color: 'rgba(255, 255, 255, 0.2)'
            }
        },
        axisLabel: {
            color: 'rgba(255, 255, 255, 0.6)',
            fontSize: 10
        },
        splitLine: {
            show: true,
            lineStyle: {
                type: 'dashed',
                color: 'rgba(255, 255, 255, 0.1)'
            }
        }
    },
    legend: {
        data: ['昨天', '今天'],
        right: 0,
        top: 0,
        textStyle: {
            color: 'rgba(255, 255, 255, 0.6)',
            fontSize: 10
        },
        itemWidth: 16,
        itemHeight: 8
    },
    series: [
        {
            name: '昨天',
            type: 'line',
            data: [],
            smooth: true,
            animation: false,
            symbol: 'circle',
            showSymbol: false,
            lineStyle: {
                width: 2
            }
        },
        {
            name: '今天',
            type: 'line',
            data: [],
            smooth: true,
            animation: false,
            symbol: 'circle',
            showSymbol: false,
            lineStyle: {
                width: 2
            }
        }
    ]
};

export default {
    name: 'RealTimeFlowCard',
    mixins: [commonMixins],
    props: {
        level: {
            type: String,
            default: 'province'
        },
        cityId: {
            type: Number,
            default: null
        }
    },
    components: {
        SectionCard
    },
    data() {
        return {
            flowChart: null,
            flowChartCity: {},
            resizeObservers: [], // 修改为数组以存储所有的观察者
            activeType: 1,
            btnGroup: [
                {
                    label: '4G流量：',
                    value: '-',
                    type: 1,
                    typeName: '4G'
                },
                {
                    label: '5G流量：',
                    value: '-',
                    type: 2,
                    typeName: '5G'
                }
            ],
            cityData: {
                '4G': {
                    label: '4G总流量：',
                    value: '-'
                },
                '5G': {
                    label: '5G总流量：',
                    value: '-'
                }
            }
        };
    },
    watch: {
        cityId: {
            handler(newVal) {
                if (this.level === 'province') {
                    this.$nextTick(() => {
                        this.initFlowChart();
                        // 省级初始化时请求两种类型的数据
                        this.btnGroup.forEach((item) => {
                            this.getRealTimeFlowData(item.type);
                        });
                    });
                } else {
                    this.$nextTick(() => {
                        this.initCityFlowChart();
                        // 市级别同时请求4G和5G数据
                        this.btnGroup.forEach((item) => {
                            this.getRealTimeFlowData(item.type);
                        });
                    });
                }
            },
            immediate: true
        }
    },
    created() {
        this.$emit('changeType', this.activeType);
    },
    methods: {
        /**
         * 格式化数字显示
         * @param {number} num - 要格式化的数字
         * @returns {string} - 格式化后的字符串
         */
        formatNumber(num) {
            if (num === 0) return '0';
            if (num < 1) {
                // 如果整数位是0，保留所有有效小数位
                return num.toString().replace(/0+$/, '');
            }
            // 其他情况保留两位小数
            return num.toFixed(2).replace(/\.?0+$/, '');
        },
        /**省 */
        handleClick(type) {
            this.activeType = type;
            this.$emit('changeType', type);
            if (this.level === 'province') {
                this.getRealTimeFlowData(type);
            }
        },
        initFlowChart() {
            if (this.$refs.flowChart) {
                // 清理旧的实例
                if (this.flowChart) {
                    this.flowChart.dispose();
                }
                this.flowChart = echarts.init(this.$refs.flowChart);
                const option = {
                    ...chartsOption
                };
                this.flowChart.setOption(option);
                const observer = new ResizeObserver(() => {
                    this.flowChart.resize();
                });
                observer.observe(this.$refs.flowChart);
                this.resizeObservers.push(observer);
            }
        },
        getRealTimeFlowData(type) {
            const params = {
                startTime: dayjs().format('YYYY-MM-DD 00:00:00'),
                endTime: dayjs().format('YYYY-MM-DD 23:59:59'),
                cityId: this.cityId || undefined,
                collectionType: this.btnGroup.find((item) => item.type === type).typeName
            };

            this.$exloading1x();
            this.getPost(
                'post',
                'softPurchaseDataTrend',
                params,
                '系统成效-软采数据趋势',
                (data) => {
                    if (
                        data &&
                        data.today &&
                        Array.isArray(data.today) &&
                        data.yesterday &&
                        Array.isArray(data.yesterday)
                    ) {
                        // 获取时间轴数据
                        const timeAxis = data.today.map((item) => item.time);

                        // 处理今天和昨天的数据，确保按时间顺序对应
                        const todayMap = new Map(data.today.map((item) => [item.time, item.total]));
                        const yesterdayMap = new Map(
                            data.yesterday.map((item) => [
                                dayjs(item.time).add(1, 'day').format('YYYY-MM-DD HH:mm:ss'),
                                item.total
                            ])
                        );

                        const todayData = timeAxis.map((time) =>
                            Number(((todayMap.get(time) || 0) / 10000).toFixed(2))
                        );
                        const yesterdayData = timeAxis.map((time) =>
                            Number(((yesterdayMap.get(time) || 0) / 10000).toFixed(2))
                        );

                        // 更新按钮组显示的总量
                        if (this.level === 'province') {
                            let todayTotal = todayData.reduce((sum, val) => sum + val, 0);
                            const btnItem = this.btnGroup.find((item) => item.type === type);
                            if (btnItem) {
                                btnItem.value = this.formatNumber(todayTotal);
                            }
                        } else {
                            // 市级别更新对应类型的总量
                            let todayTotal = todayData.reduce((sum, val) => sum + val, 0);
                            const typeName = this.btnGroup.find(
                                (item) => item.type === type
                            ).typeName;
                            if (this.cityData[typeName]) {
                                this.cityData[typeName].value = this.formatNumber(todayTotal);
                            }
                        }

                        // 更新图表数据
                        const option = {
                            xAxis: {
                                data: timeAxis
                            },
                            series: [
                                {
                                    name: '昨天',
                                    data: yesterdayData
                                },
                                {
                                    name: '今天',
                                    data: todayData
                                }
                            ]
                        };

                        if (this.level === 'province') {
                            this.flowChart && this.flowChart.setOption(option);
                        } else {
                            const typeName = this.btnGroup.find(
                                (item) => item.type === type
                            ).typeName;
                            this.flowChartCity[typeName] &&
                                this.flowChartCity[typeName].setOption(option);
                        }
                    }
                    this.$exloaded1x();
                }
            );
        },
        /**市 */
        initCityFlowChart() {
            Object.keys(this.cityData).forEach((key) => {
                const chart = document.getElementById('flowChart-' + key);
                if (chart) {
                    // 清理旧的实例
                    if (this.flowChartCity[key]) {
                        this.flowChartCity[key].dispose();
                    }
                    this.flowChartCity[key] = echarts.init(chart);
                    const option = {
                        ...chartsOption
                    };
                    this.flowChartCity[key].setOption(option);
                    const observer = new ResizeObserver(() => {
                        this.flowChartCity[key].resize();
                    });
                    observer.observe(chart);
                    this.resizeObservers.push(observer);
                }
            });
        }
    },
    beforeDestroy() {
        // 清理所有的 ResizeObserver
        this.resizeObservers.forEach((observer) => {
            observer.disconnect();
        });
        this.resizeObservers = [];

        // 清理省级图表实例
        if (this.flowChart) {
            this.flowChart.dispose();
            this.flowChart = null;
        }

        // 清理市级图表实例
        Object.values(this.flowChartCity).forEach((chart) => {
            if (chart) {
                chart.dispose();
            }
        });
        this.flowChartCity = {};
    }
};
</script>

<style lang="less" scoped>
.real-time-flow {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding-top: 0.8889rem;
    &.city-flow {
        padding-top: 0;
    }

    .network-flow {
        display: flex;
        justify-content: center;
        margin-bottom: 0.8889rem;
        gap: 0.5556rem;

        .flow-item {
            cursor: pointer;
            user-select: none;
            min-width: 45%;
            height: 2.2222rem;
            display: flex;
            align-items: center;
            justify-content: center;
            background-image: url('../../../../img/effectivenessScreen/real-flow-btn.png');
            background-size: 100% 100%;
            background-repeat: no-repeat;
            .flow-label {
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 0.7778rem;
                color: #b0d7ff;
                line-height: 0.7778rem;
            }
            .flow-value {
                color: #b0d7ff;
                font-family: YouSheBiaoTiHei;
                font-size: 0.8889rem;
                line-height: 0.8889rem;
                span {
                    font-family: YouSheBiaoTiHei;
                    font-size: 0.6667rem;
                    line-height: 0.6667rem;
                }
            }

            &.active {
                background-image: url('../../../../img/effectivenessScreen/real-flow-btn-active.png');

                .flow-label {
                    color: #ffffff;
                    text-shadow: 0px 0px 5px rgba(30, 198, 255, 0.8);
                }
                .flow-value {
                    background: linear-gradient(180deg, #ffffff 0%, #9be5ff 50%, #0dcaf5 80%);
                    -webkit-background-clip: text;
                    background-clip: text;
                    color: transparent;
                }
            }
        }
    }
    .flow-city-item {
        padding-top: 0.8889rem;
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        gap: 0.4444rem;
        position: relative;
        &:not(:last-child)::before {
            content: '';
            position: absolute;
            bottom: -0.4444rem;
            left: 0;
            width: 100%;
            height: 0.0556rem;
            background: linear-gradient(
                to right,
                rgba(25, 235, 255, 1) 0%,
                rgba(25, 235, 255, 1) 0.4444rem,
                rgba(0, 139, 255, 0.4) 0.4444rem,
                rgba(0, 139, 255, 0.4) calc(100% - 0.4444rem),
                rgba(25, 235, 255, 1) calc(100% - 0.4444rem),
                rgba(25, 235, 255, 1) 100%
            );
        }
        .flow-city-header {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 66%;
            height: 2.6667rem;
            background: linear-gradient(
                270deg,
                rgba(9, 118, 255, 0.21) 0%,
                rgba(9, 155, 255, 0.04) 100%
            );
            position: relative;

            .flow-label {
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 0.7778rem;
                line-height: 0.7778rem;
                color: #ffffff;
                text-shadow: 0px 0px 5px rgba(30, 198, 255, 0.8);
            }
            .flow-value {
                background: linear-gradient(180deg, #ffffff 0%, #9be5ff 50%, #0dcaf5 80%);
                -webkit-background-clip: text;
                background-clip: text;
                color: transparent;
                font-family: YouSheBiaoTiHei;
                font-size: 1.1111rem;
                line-height: 1.1111rem;
                span {
                    font-family: YouSheBiaoTiHei;
                    font-size: 0.6667rem;
                    line-height: 0.6667rem;
                }
            }
            &:after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 2.6667rem;
                height: 2.6667rem;
                background-image: url('../../../../img/effectivenessScreen/icon-flow.png');
                background-size: 100% 100%;
                background-repeat: no-repeat;
            }
        }
    }

    .flow-chart {
        flex: 1;
        width: 100%;
    }
}
</style>
