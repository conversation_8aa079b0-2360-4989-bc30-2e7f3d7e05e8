<template>
    <SectionCard class="location-accuracy-card" title="指纹模型定位精度">
        <template #titleRight v-if="level === 'province'">
            <div class="btn-box">
                <div
                    class="btn-box-item"
                    :class="{ active: item.value === activeBtn }"
                    @click="handleBtnClick(item)"
                    v-for="(item, index) in btnList"
                    :key="index"
                >
                    <span>{{ item.label }}</span>
                </div>
            </div>
        </template>
        <div class="accuracy-content" v-if="level === 'province'">
            <div class="accuracy-charts">
                <div class="accuracy-indicator">
                    <div class="accuracy-icon">
                        <img src="../../../../img/effectivenessScreen/accuracy-4G.png" alt="4G" />
                    </div>
                    <div class="accuracy-info">
                        <div class="accuracy-label">50米精度(%)</div>
                        <div class="accuracy-value">{{ accuracy50 }}</div>
                    </div>
                </div>
                <div class="accuracy-indicator">
                    <div class="accuracy-icon">
                        <img src="../../../../img/effectivenessScreen/accuracy-5G.png" alt="5G" />
                    </div>
                    <div class="accuracy-info">
                        <div class="accuracy-label">100米精度(%)</div>
                        <div class="accuracy-value">{{ accuracy100 }}</div>
                    </div>
                </div>
            </div>

            <SectionCard class="accuracy-level-card" title="50米精度等级" titleBg="title-bg2">
                <div class="level-list custom-scrollbar">
                    <div class="level-item" v-for="(item, index) in level50List" :key="index">
                        <div class="level-rank">{{ index + 1 }}</div>
                        <div class="level-detail">
                            <div class="level-detail-left">
                                <div class="level-name">{{ item.name }}</div>
                                <div class="level-bar">
                                    <div
                                        class="level-bar-inner"
                                        style="--bar-inner-color: #008bff"
                                        :style="{ width: item.value + '%' }"
                                    >
                                        <div class="level-bar-inner-cursor"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="level-value">{{ item.value }}%</div>
                        </div>
                    </div>
                </div>
            </SectionCard>

            <SectionCard class="accuracy-level-card" title="100米精度等级" titleBg="title-bg2">
                <div class="level-list custom-scrollbar">
                    <div class="level-item" v-for="(item, index) in level100List" :key="index">
                        <div class="level-rank">{{ index + 1 }}</div>
                        <div class="level-detail">
                            <div class="level-detail-left">
                                <div class="level-name">{{ item.name }}</div>
                                <div class="level-bar">
                                    <div
                                        class="level-bar-inner"
                                        style="--bar-inner-color: #19ebff"
                                        :style="{ width: item.value + '%' }"
                                    >
                                        <div class="level-bar-inner-cursor"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="level-value">{{ item.value }}%</div>
                        </div>
                    </div>
                </div>
            </SectionCard>
        </div>
        <div class="accuracy-content city-content" v-else>
            <div class="accuracy-item" v-for="(item, index) in itemList" :key="index">
                <div class="accuracy-item-img">
                    <img :src="item.img" alt="4G" />
                </div>
                <div class="accuracy-item-info">
                    <div class="accuracy-item-title">{{ item.title }}</div>
                    <div class="accuracy-item-content">
                        <div
                            class="accuracy-item-content-item"
                            v-for="(itemChild, indexChild) in item.content"
                            :key="indexChild"
                        >
                            <div class="accuracy-item-content-item-label">
                                {{ itemChild.label }}
                            </div>
                            <div class="accuracy-item-content-item-value">
                                {{ itemChild.value }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </SectionCard>
</template>

<script>
import SectionCard from './SectionCard.vue';
import commonMixins from '@/script/mixins/commonMixins.js';

export default {
    name: 'LocationAccuracyCard',
    props: {
        level: {
            type: String,
            default: 'province'
        }
    },
    components: {
        SectionCard
    },
    mixins: [commonMixins],
    data() {
        return {
            /* 省 */
            activeBtn: 1,
            btnList: [
                { label: '4G', value: 1 },
                { label: '5G', value: 2 }
            ],
            // 精度数值
            accuracy50: '--',
            accuracy100: '--',
            // 等级列表
            level50List: [],
            level100List: [],
            /* 市 */
            itemList: [
                {
                    img: require('../../../../img/effectivenessScreen/accuracy-4G-big.png'),
                    title: '4G定位精度',
                    content: [
                        {
                            label: '50米精度(%)',
                            field: 'average50MRatio',
                            value: '--'
                        },
                        {
                            label: '100米精度(%)',
                            field: 'average100MRatio',
                            value: '--'
                        }
                    ]
                },
                {
                    img: require('../../../../img/effectivenessScreen/accuracy-5G-big.png'),
                    title: '5G定位精度',
                    content: [
                        {
                            label: '50米精度(%)',
                            field: 'average50MRatio',
                            value: '--'
                        },
                        {
                            label: '100米精度(%)',
                            field: 'average100MRatio',
                            value: '--'
                        }
                    ]
                }
            ]
        };
    },
    watch: {
        level: {
            handler(newVal) {
                if (newVal === 'province') {
                    this.fetchAllData();
                } else {
                    this.fetchAllAccuracy();
                }
            }
        }
    },
    created() {
        this.fetchAllData();
    },
    methods: {
        /**
         * 处理按钮点击
         */
        handleBtnClick(item) {
            if (this.activeBtn === item.value) return;
            this.activeBtn = item.value;
            this.fetchAllData();
        },
        /** 获取总体指纹精度 */
        fetchAccuracy() {
            const params = { networkType: this.activeBtn };
            this.getPost('post', 'fingerprintTotal', params, '总体指纹精度', (data) => {
                if (!data) return;
                // 保留两位小数，避免 NaN
                this.accuracy50 = (Number(data.average50MRatio || 0) * 100).toFixed(2);
                this.accuracy100 = (Number(data.average100MRatio || 0) * 100).toFixed(2);
            });
        },
        /** 获取地市精度排行 */
        fetchRank() {
            const params = { networkType: this.activeBtn };
            this.getPost('post', 'fingerprintCityRank', params, '地市精度排行', (data) => {
                if (!Array.isArray(data)) return;
                // 先根据不同精度指标进行排序(降序)，再生成列表数据
                const by50Desc = [...data].sort(
                    (a, b) => Number(b.average50MRatio || 0) - Number(a.average50MRatio || 0)
                );
                const by100Desc = [...data].sort(
                    (a, b) => Number(b.average100MRatio || 0) - Number(a.average100MRatio || 0)
                );

                this.level50List = by50Desc.map((item) => ({
                    name: item.cityName,
                    value: (Number(item.average50MRatio || 0) * 100).toFixed(2)
                }));

                this.level100List = by100Desc.map((item) => ({
                    name: item.cityName,
                    value: (Number(item.average100MRatio || 0) * 100).toFixed(2)
                }));
                this.$emit(
                    'level100ListChange',
                    this.level100List,
                    { 1: '4G', 2: '5G' }[this.activeBtn]
                );
            });
        },
        fetchAllAccuracy() {
            this.getPost(
                'post',
                'fingerprintTotal',
                { networkType: 1 },
                '总体指纹精度4G',
                (data) => {
                    if (!data) return;
                    this.itemList[0].content[0].value = (
                        Number(data[this.itemList[0].content[0].field] || 0) * 100
                    ).toFixed(2);
                    this.itemList[0].content[1].value = (
                        Number(data[this.itemList[0].content[1].field] || 0) * 100
                    ).toFixed(2);
                }
            );
            this.getPost(
                'post',
                'fingerprintTotal',
                { networkType: 2 },
                '总体指纹精度5G',
                (data) => {
                    if (!data) return;
                    this.itemList[1].content[0].value = (
                        Number(data[this.itemList[1].content[0].field] || 0) * 100
                    ).toFixed(2);
                    this.itemList[1].content[1].value = (
                        Number(data[this.itemList[1].content[1].field] || 0) * 100
                    ).toFixed(2);
                }
            );
        },
        fetchAllData() {
            this.fetchAccuracy();
            this.fetchRank();
        }
    }
};
</script>

<style lang="less" scoped>
.location-accuracy-card {
    min-height: 0;
    flex: 1;
}
.btn-box {
    user-select: none;
    display: flex;
    gap: 0.2222rem;

    span {
        font-family: AlibabaPuHuiTi, AlibabaPuHuiTi;
        font-weight: 400;
        font-size: 0.6667rem;
        color: #8eb8ea;
    }

    .btn-box-item {
        cursor: pointer;
        width: 3rem;
        height: 1.3333rem;
        border-radius: 0.2222rem;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(1, 26, 66, 0.502);
        border: 0.0556rem solid rgba(0, 149, 255, 0.5);

        &.active {
            background: #092d59;
            box-shadow: inset 0.3333rem 0px 0.6667rem 0px #1772c6,
                inset 0px 0px 0.3333rem 0px #1772c6;
            border: 1px solid #00ceff;

            span {
                font-weight: 600;
                color: #00ceff;
                text-shadow: 0px 0px 10px rgba(255, 255, 255, 0.25);
            }
        }
    }
}

.accuracy-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 0.5556rem;
    &.city-content {
        padding: 0.8889rem 0.8889rem 0;
    }

    .accuracy-charts {
        display: flex;
        align-items: center;
        padding: 0.8889rem;
        padding-bottom: 0;

        .accuracy-indicator {
            flex: 1;
            display: flex;
            align-items: center;
            height: 100%;

            .accuracy-icon {
                img {
                    width: 6rem;
                    height: 6rem;
                }
            }

            .accuracy-info {
                height: 100%;
                display: flex;
                flex-direction: column;
                justify-content: center;
                gap: 0.4444rem;
                margin-left: 0.6667rem;

                .accuracy-label {
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 0.7778rem;
                    color: #ffffff;
                    line-height: 0.7778rem;
                    text-shadow: 0px 0px 0.2778rem rgba(30, 198, 255, 0.8);
                }

                .accuracy-value {
                    background: linear-gradient(180deg, #ffffff 0%, #9be5ff 50%, #0dcaf5 80%);
                    -webkit-background-clip: text;
                    background-clip: text;
                    color: transparent;
                    font-family: YouSheBiaoTiHei;
                    font-size: 1.3333rem;
                }
            }
        }
    }

    .accuracy-level-card {
        min-height: 0;
        flex: 1;

        .level-list {
            --scrollbar-width: 0.1667rem;
            padding-top: 0.4444rem;
            padding-bottom: 0;
            height: 100%;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 0.6667rem;

            .level-item {
                width: 100%;
                display: flex;
                align-items: flex-start;
                gap: 0.5556rem;

                .level-rank {
                    width: 1.3333rem;
                    height: 1.3333rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    justify-self: flex-start;
                    color: #ffffff;
                    font-size: 0.7778rem;
                    background-image: url('../../../../img/effectivenessScreen/index-bg.png');
                    background-size: 100% 100%;
                    background-repeat: no-repeat;
                    background-position: top;
                }
                .level-detail {
                    flex: 1;
                    display: flex;
                    justify-content: space-between;
                    gap: 0.4444rem;
                    .level-detail-left {
                        flex: 1;
                        display: flex;
                        flex-direction: column;
                        gap: 0.2778rem;
                        .level-name {
                            font-family: PingFangSC, PingFang SC;
                            font-weight: 400;
                            font-size: 0.7778rem;
                            color: #ffffff;
                            line-height: 1.1111rem;
                            text-shadow: 0px 0px 5px rgba(30, 198, 255, 0.8);
                        }

                        .level-bar {
                            height: 0.5556rem;
                            background-color: rgba(0, 139, 255, 0.2);
                            .level-bar-inner {
                                --bar-inner-color: #008bff;
                                height: 100%;
                                background: linear-gradient(
                                    270deg,
                                    var(--bar-inner-color) 0%,
                                    rgba(0, 139, 255, 0) 100%
                                );
                                position: relative;
                                .level-bar-inner-cursor {
                                    position: absolute;
                                    right: 0;
                                    top: 50%;
                                    transform: translateY(-50%);
                                    width: 0.1667rem;
                                    height: 1.1111rem;
                                    background: #affaff;
                                    box-shadow: 0px 0.1111rem 1rem 0.0556rem
                                            rgba(30, 166, 244, 0.96),
                                        0px 0px 0.1111rem 0.0556rem rgba(30, 166, 244, 0.57);
                                    border-radius: 0.0556rem;
                                }
                            }
                        }
                    }
                    .level-value {
                        background: linear-gradient(
                            180deg,
                            #ffffff 0%,
                            #ffffff 60%,
                            #9be5ff 80%,
                            #0dcaf5 100%
                        );
                        -webkit-background-clip: text;
                        background-clip: text;
                        color: transparent;
                        font-family: DIN, DIN;
                        font-weight: 500;
                        font-size: 0.7778rem;
                        line-height: 0.7778rem;
                        display: flex;
                        align-items: flex-end;
                    }
                }
            }
        }
    }

    .accuracy-item {
        min-height: 0;
        flex: 1;
        display: flex;
        align-items: center;
        gap: 0.8889rem;
        .accuracy-item-img {
            img {
                width: 9.7778rem;
                height: 9.7778rem;
            }
        }
        .accuracy-item-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 0.8889rem;
            .accuracy-item-title {
                width: 100%;
                height: 1.4444rem;
                display: flex;
                align-items: center;
                background-image: url('../../../../img/effectivenessScreen/section-card-bg2.png');
                background-size: 100% 100%;
                background-repeat: no-repeat;
                padding-left: 0.8889rem;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 0.7778rem;
                color: #ffffff;
                text-shadow: 0px 0px 0.2778rem rgba(30, 198, 255, 0.8);
            }
            .accuracy-item-content {
                height: 4.4444rem;
                background: linear-gradient(
                    270deg,
                    rgba(9, 155, 255, 0.04) 0%,
                    rgba(9, 118, 255, 0.21) 100%
                );
                position: relative;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                &:after {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: -0.1111rem;
                    width: 0.1111rem;
                    height: 100%;
                    background: linear-gradient(180deg, #0085ff 0%, #00d4ff 100%);
                }
                .accuracy-item-content-item {
                    width: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    &-label {
                        width: 50%;
                        padding-left: 0.8889rem;
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 400;
                        font-size: 0.7778rem;
                        color: #ffffff;
                        line-height: 0.7778rem;
                        text-shadow: 0px 0px 0.2778rem rgba(30, 198, 255, 0.8);
                    }

                    &-value {
                        width: 50%;
                        max-width: 50%;
                        overflow: hidden;
                        padding-left: 0.4444rem;
                        background: linear-gradient(180deg, #ffffff 0%, #9be5ff 50%, #0dcaf5 80%);
                        -webkit-background-clip: text;
                        background-clip: text;
                        color: transparent;
                        font-family: YouSheBiaoTiHei;
                        font-size: 1.3333rem;
                    }
                }
            }
        }
    }
}
</style>
