<template>
    <div class="effectiveness-screen">
        <div class="effectiveness-screen-item item-left rotate-left">
            <MRSamplingCard
                class="left-section-card card-1"
                :level="nowLevel"
                :cityId="nowCityId"
            />
            <RealTimeFlowCard
                class="left-section-card card-2"
                :level="nowLevel"
                :cityId="nowCityId"
                @changeType="handleChangeType"
            />
            <AlarmMonitorCard
                class="left-section-card card-3"
                v-if="nowLevel === 'province'"
                :level="nowLevel"
            />
        </div>
        <div class="effectiveness-screen-item item-center rotate-center">
            <CenterGisMap
                class="center-gis-map"
                :data="centerMapData"
                @changeRegion="handleChangeRegion"
            />
        </div>
        <div class="effectiveness-screen-item item-right rotate-right">
            <CapabilityCard class="right-section-card" :level="nowLevel" />
            <LocationAccuracyCard
                class="right-section-card"
                :level="nowLevel"
                @level100ListChange="handleLevel100ListChange"
            />
        </div>
        <div class="effectiveness-screen-bottom">
            <BottomData v-show="nowLevel === 'province'" />
        </div>
    </div>
</template>

<script>
import commonMixins from '@/script/mixins/commonMixins.js';
import MRSamplingCard from './components/MRSamplingCard.vue';
import RealTimeFlowCard from './components/RealTimeFlowCard.vue';
import AlarmMonitorCard from './components/AlarmMonitorCard.vue';
import CapabilityCard from './components/CapabilityCard.vue';
import LocationAccuracyCard from './components/LocationAccuracyCard.vue';
import CenterGisMap from './components/CenterGisMap.vue';
import BottomData from './components/BottomData.vue';

export default {
    name: 'effectivenessScreen',
    mixins: [commonMixins],
    components: {
        MRSamplingCard,
        RealTimeFlowCard,
        AlarmMonitorCard,
        CapabilityCard,
        LocationAccuracyCard,
        CenterGisMap,
        BottomData
    },
    data() {
        return {
            centerMapData: {
                softCollectionRankData: [],
                level100List: [],
                level100Type: ''
            },
            nowLevel: 'province',
            nowCityId: null
        };
    },
    methods: {
        handleChangeType(type) {
            const params = { networkType: type };
            this.getPost('post', 'softCollectionRank', params, '实时软采流量查询', (data) => {
                if (type === 1) {
                    data = [
                        { cityId: '370100', cityName: '济南市', total: 10 },
                        { cityId: '370200', cityName: '青岛市', total: 20 },
                        { cityId: '370300', cityName: '淄博市', total: 30 },
                        { cityId: '370400', cityName: '枣庄市', total: 40 },
                        { cityId: '370500', cityName: '东营市', total: 50 },
                        { cityId: '370600', cityName: '烟台市', total: 60 },
                        { cityId: '370700', cityName: '潍坊市', total: 70 },
                        { cityId: '370800', cityName: '济宁市', total: 80 },
                        { cityId: '370900', cityName: '泰安市', total: 90 },
                        { cityId: '371000', cityName: '威海市', total: 100 },
                        { cityId: '371100', cityName: '日照市', total: 110 },
                        { cityId: '371300', cityName: '临沂市', total: 120 },
                        { cityId: '371400', cityName: '德州市', total: 130 },
                        { cityId: '371500', cityName: '聊城市', total: 140 },
                        { cityId: '371600', cityName: '滨州市', total: 150 }
                        // { cityId: '371700', cityName: '菏泽市', total: 160 }
                    ];
                } else {
                    data = [
                        { cityId: '370100', cityName: '济南市', total: 2400 },
                        { cityId: '370200', cityName: '青岛市', total: 2300 },
                        { cityId: '370300', cityName: '淄博市', total: 2200 },
                        { cityId: '370400', cityName: '枣庄市', total: 2100 },
                        { cityId: '370500', cityName: '东营市', total: 2000 },
                        { cityId: '370600', cityName: '烟台市', total: 1900 },
                        { cityId: '370700', cityName: '潍坊市', total: 1800 },
                        { cityId: '370800', cityName: '济宁市', total: 1700 },
                        { cityId: '370900', cityName: '泰安市', total: 1600 },
                        { cityId: '371000', cityName: '威海市', total: 1500 },
                        { cityId: '371100', cityName: '日照市', total: 1400 },
                        { cityId: '371300', cityName: '临沂市', total: 1100 },
                        { cityId: '371400', cityName: '德州市', total: 1200 },
                        { cityId: '371500', cityName: '聊城市', total: 1300 },
                        { cityId: '371600', cityName: '滨州市', total: 1400 }
                        // { cityId: '371700', cityName: '菏泽市', total: 2500 }
                    ];
                }
                if (data && Array.isArray(data)) {
                    this.centerMapData.softCollectionRankData = data.sort(
                        (a, b) => b.total - a.total
                    );
                }
            });
        },
        handleChangeRegion({ name, adcode, level }) {
            this.nowLevel = level;
            if (level === 'province') {
                this.nowCityId = null;
            } else {
                this.nowCityId = adcode;
            }
        },
        handleLevel100ListChange(list, type) {
            this.centerMapData.level100List = list;
            this.centerMapData.level100Type = type;
        },
        handleMouseEnter() {
            document
                .querySelector('.effectiveness-screen-item.item-left')
                .classList.add('rotate-left');
            document
                .querySelector('.effectiveness-screen-item.item-right')
                .classList.add('rotate-right');
            document
                .querySelector('.effectiveness-screen-item.item-center')
                .classList.add('rotate-center');
        },
        handleMouseLeave() {
            document
                .querySelector('.effectiveness-screen-item.item-left')
                .classList.remove('rotate-left');
            document
                .querySelector('.effectiveness-screen-item.item-right')
                .classList.remove('rotate-right');
            document
                .querySelector('.effectiveness-screen-item.item-center')
                .classList.remove('rotate-center');
        }
    }
};
</script>

<style lang="less" scoped>
.effectiveness-screen {
    width: 100%;
    height: 100%;
    background-image: url('../../../img/effectivenessScreen/main-bg.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    gap: 1.3333rem;
    padding: 0 1.3333rem;

    .effectiveness-screen-item {
        transform: translateY(-1.5rem);
        background-size: 100% 100%;
        background-repeat: no-repeat;
        position: relative;

        &.item-left {
            width: calc(28% - 1.3333rem);
            height: 90%;
            background-image: url('../../../img/effectivenessScreen/left-normal-bg.png');
            padding: 1.3333rem 1.3333rem 2.2222rem 1.5556rem;
            display: flex;
            flex-direction: column;
            gap: 0.8889rem;

            .left-section-card {
                min-height: 0;
                &.card-1 {
                    height: 30%;
                }
                &.card-2 {
                    flex: 1;
                }
                &.card-3 {
                    flex: 1;
                }
            }
            transform-origin: left center;
            transition: transform 0.5s ease, width 0.5s ease;
            &.rotate-left {
                width: calc(32% - 1.3333rem);
                transform: translateY(-1.5rem) perspective(83.3333rem) rotateY(24deg);
            }
        }

        &.item-center {
            min-width: 44%;
            width: 44%;
            height: 78%;
            background-image: url('../../../img/effectivenessScreen/center-normal-bg.png');
            padding: 0.6667rem 0.6667rem 2rem 0.6667rem;
            position: absolute;
            left: 50%;
            transform: translate(-50%, -2.6667rem);
            .center-gis-map {
                width: 100%;
                height: 100%;
            }
            transition: transform 0.5s ease, width 0.5s ease;
            &.rotate-center {
                background-image: url('../../../img/effectivenessScreen/center-rotate-bg.png');
                transform: translate(-50%, -2.6667rem);
                width: 48%;
            }
        }

        &.item-right {
            width: calc(28% - 1.3333rem);
            height: 90%;
            background-image: url('../../../img/effectivenessScreen/right-normal-bg.png');
            padding: 1.3333rem 1.3333rem 2.2222rem 1.5556rem;
            display: flex;
            flex-direction: column;
            gap: 0.8889rem;

            .right-section-card {
                height: fit-content;
            }
            transform-origin: right center;
            transition: transform 0.5s ease, width 0.5s ease;
            &.rotate-right {
                width: calc(32% - 1.3333rem);
                transform: translateY(-1.5rem) perspective(83.3333rem) rotateY(-24deg);
            }
        }
    }

    .effectiveness-screen-bottom {
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 64%;
        height: 14%;
        background-image: url('../../../img/effectivenessScreen/bottom-bg.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        display: flex;
        justify-content: center;
        align-items: center;
    }
}
</style>
