<template>
    <div class="gold-approval-mask" v-if="dialogVisible" @click.self="null">
        <div
            class="gold-approval-container"
            :class="{ 'gold-approval-container-2x': currentStep === 1 }"
        >
            <header class="gold-approval-header">
                <span class="gold-approval-title">{{ title }}</span>
            </header>
            <i class="el-icon-close gold-approval-close" @click="handleClose"></i>
            <main class="gold-approval-main">
                <div class="gold-approval-main-content">
                    <div class="gold-approval-steps">
                        <div
                            v-for="(item, index) in approvalSteps"
                            :key="index"
                            class="gold-approval-step-wrapper"
                        >
                            <div
                                class="gold-approval-step"
                                :class="{
                                    process: item.status === 'process',
                                    finish: item.status === 'finish'
                                }"
                            ></div>
                            <div
                                v-if="index < approvalSteps.length - 1"
                                class="gold-approval-step-line"
                                :class="{
                                    process: item.status === 'process',
                                    finish: item.status === 'finish'
                                }"
                            ></div>
                        </div>
                    </div>
                    <!-- 步骤1：审批人选择和理由 -->
                    <template v-if="currentStep === 1">
                        <div class="gold-approval-main-layout">
                            <div class="gold-approval-left-panel">
                                <el-select
                                    v-model="approverValue"
                                    placeholder="审批人选择"
                                    class="gold-approval-approver-select"
                                    popper-class="gold-approval-select-popper"
                                >
                                    <template slot="prefix">
                                        <img
                                            src="@/img/goldApproval/user-icon.png"
                                            alt="user-icon"
                                            class="gold-approval-user-icon"
                                        />
                                    </template>
                                    <el-option
                                        v-for="item in approverOptions"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    ></el-option>
                                </el-select>
                                <div class="gold-approval-apply-reason">
                                    <div class="gold-approval-apply-reason-item">
                                        <span class="gold-approval-apply-reason-label"
                                            >申请原因：</span
                                        >
                                        <el-select
                                            v-model="reasonValue"
                                            placeholder="请选择"
                                            class="gold-approval-apply-reason-select"
                                            popper-class="gold-approval-select-popper"
                                        >
                                            <el-option
                                                v-for="item in reasonOptions"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value"
                                            ></el-option>
                                        </el-select>
                                        <span></span>
                                        <el-input
                                            placeholder="请输入工单号"
                                            class="gold-approval-apply-reason-input"
                                            v-model.trim="workOrder"
                                            clearable
                                        ></el-input>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>

                    <!-- 步骤2：验证码 -->
                    <template v-if="currentStep === 2">
                        <el-input
                            placeholder="输入验证码"
                            class="gold-approval-verify-code"
                            v-model.trim="verifyCode"
                        >
                            <template slot="prefix">
                                <img
                                    src="@/img/goldApproval/safe-icon.png"
                                    alt="safe-icon"
                                    class="gold-approval-safe-icon"
                                />
                            </template>
                            <template slot="suffix">
                                <span
                                    class="gold-approval-code__msg"
                                    :class="{ resend: isResend }"
                                    @click="handleResend"
                                >
                                    {{ isResend ? '重新发送' : '金库验证码已发送' }}
                                </span>
                            </template>
                        </el-input>
                        <div
                            class="gold-approval-verify-result"
                            v-if="verifySubmitted && verifyResultReceived"
                        >
                            <i
                                class="gold-approval-verify-result-icon"
                                :class="isVerifySuccess ? 'el-icon-success' : 'el-icon-warning'"
                            ></i>
                            <span class="gold-approval-verify-result-text"
                                >{{ isVerifySuccess ? '验证成功' : '验证失败' }}
                            </span>
                        </div>
                    </template>

                    <el-button
                        type="primary"
                        class="gold-approval-submit-btn"
                        @click="handleSubmit"
                        :disabled="isSubmitDisabled"
                        :loading="loading"
                    >
                        {{ submitBtnText }}
                    </el-button>
                </div>
            </main>
        </div>
    </div>
</template>

<script>
/**
 * 金库审批弹窗
 * 1、在src\script\api\module\regionResource.js中添加需要审批的接口配置
 * 2、将请求接口原本的getPost方法改为getPostGoldApproval方法，用法一致
 */
import { request } from '@/script/utils/request.js';
import goldApprovalService from '@/script/api/module/goldApproval.js';

export default {
    name: 'GoldApproval',
    props: {
        // 弹窗标题
        title: {
            type: String,
            default: '金库审批'
        },
        ids: {
            type: Object,
            default: () => ({
                assetId: '',
                sceneId: ''
            })
        }
    },
    data() {
        return {
            dialogVisible: true,
            currentStep: 1,
            loading: false, // 按钮loading状态
            approvalSteps: [
                {
                    status: 'process'
                },
                {
                    status: 'wait'
                }
            ],
            // 验证码相关
            verifyCode: '',
            isResend: true,
            verifySubmitted: false,
            verifyResultReceived: false,
            // 审批人选择
            approverValue: '',
            approverOptions: [],
            // 申请原因相关
            reasonValue: '',
            reasonOptions: [
                { label: '工单', value: '工单' },
                { label: '特定工单', value: '特定工单' },
                { label: '其他', value: '其他' }
            ],
            workOrder: '',
            // 接口返回数据存储
            approvalInfo: {
                sceneId: '',
                assetId: '',
                account: ''
            }, // 查询状态接口返回
            pantheonSerialNum: '', // 第一步接口返回
            isVerifySuccess: false
        };
    },
    watch: {
        dialogVisible: {
            handler(val) {
                if (val) {
                    this.getGoldSceneStatus();
                }
            },
            immediate: true
        }
    },
    computed: {
        // 按钮文本
        submitBtnText() {
            if (this.currentStep === 2) {
                return '确定';
            }
            return '下一步';
        },
        // 是否禁用提交按钮
        isSubmitDisabled() {
            if (this.currentStep === 1) {
                return !this.approverValue || !this.reasonValue || !this.workOrder;
            } else if (this.currentStep === 2) {
                return !this.verifyCode || (this.verifySubmitted && this.isVerifySuccess);
            }
            return false;
        },
        userEnName() {
            return frameService.getUser().name; //英文名
        }
    },
    methods: {
        // 关闭弹窗
        handleClose() {
            this.dialogVisible = false;
            this.$emit('close');
        },
        // 重新发送验证码
        handleResend() {
            if (this.isResend) {
                this.sendVerifyCode();
                // 清除验证结果
                this.verifySubmitted = false;
                this.verifyResultReceived = false;
                this.verifyCode = '';
            }
        },
        // 发送验证码
        async sendVerifyCode() {
            this.isResend = false;
            await this.getGoldSceneStatus();
            await this.applyGoldSceneRemote();
            setTimeout(() => {
                this.isResend = true;
            }, 10000); // 10秒后可重新发送
        },
        // 提交按钮点击事件
        async handleSubmit() {
            this.loading = true;
            try {
                if (this.currentStep === 1) {
                    // 第二步：申请金库场景远程授权
                    await this.applyGoldSceneRemote();
                } else if (this.currentStep === 2) {
                    // 第三步：金库场景授权二次短信认证
                    await this.submitVerifyCode();
                }
            } catch (error) {
                console.error('请求出错：', error);
            } finally {
                this.loading = false;
            }
        },
        // 切换步骤,更新步骤状态
        goToStep(step) {
            this.currentStep = step;
            this.approvalSteps = this.approvalSteps.map((item, index) => {
                if (index + 1 < step) {
                    return { status: 'finish' };
                } else if (index + 1 === step) {
                    return { status: 'process' };
                }
                return { status: 'wait' };
            });
        },
        async getGoldSceneStatus() {
            const params = {
                sceneId: this.ids.sceneId,
                assetId: this.ids.assetId,
                loginUser: this.userEnName
            };
            await this.getPost(
                'post',
                goldApprovalService.goldSceneStatus,
                params,
                '金库场景状态查询',
                (data) => {
                    if (data.sceneStatus === '0') {
                        // 处理审批人列表
                        this.approverOptions = data.approveUserList.map((item) => ({
                            label: item.approveUserName,
                            value: item.approveUserAccount
                        }));
                        this.approvalInfo.sceneId = data.sceneId;
                        this.approvalInfo.assetId = data.assetId;
                        this.approvalInfo.account = data.account;
                    } else {
                        this.$message.warning('获取不到审批人信息');
                    }
                    this.goToStep(1);
                }
            );
        },
        // 1、申请金库场景远程授权
        async applyGoldSceneRemote() {
            // 组合申请原因
            const fullReason = `因${this.reasonValue}原因, 工单号为${this.workOrder}，特此进行金库申请，请审批`;

            const params = {
                sceneId: this.approvalInfo.sceneId,
                assetId: this.approvalInfo.assetId,
                loginUser: this.userEnName,
                account: this.approvalInfo.account,
                approveUserAccount: this.approverValue,
                applyReason: fullReason,
                authMaxTime: '1'
            };
            await this.getPost(
                'post',
                goldApprovalService.applyGoldSceneRemote,
                params,
                '申请金库场景远程授权',
                (data) => {
                    this.pantheonSerialNum = data.pantheonSerialNum;
                    this.goToStep(2);
                }
            );
        },
        // 2、提交验证码
        async submitVerifyCode() {
            this.verifySubmitted = true;
            this.verifyResultReceived = false;

            const params = {
                authCode: this.verifyCode,
                pantheonSerialNum: this.pantheonSerialNum
            };

            await this.getPost(
                'post',
                goldApprovalService.secondCodeVerify,
                params,
                '金库场景授权二次短信认证',
                (data) => {
                    this.verifyResultReceived = true;
                    this.isVerifySuccess = data.resultCode === '1';

                    if (this.isVerifySuccess) {
                        this.$emit('success', {
                            sessionId: data.pantheonSessionId
                        });
                        setTimeout(() => {
                            this.handleClose();
                        }, 1000);
                    } else {
                        this.isResend = true;
                    }
                }
            );
        },
        /**
         * 接口请求封装
         * @param method 请求方式
         * @param postName 接口名
         * @param _param 入参
         * @param errContent 错误提示内容
         * @param callBack 成功逻辑业务代码callBack
         * @param catchCallback catch时回调
         */
        async getPost(method, postName, _param, errContent, callBack, catchCallback) {
            await request(method, postName, _param)
                .then((rcvData) => {
                    this.$exloaded1x();
                    //此处成功逻辑业务代码
                    if (typeof rcvData === 'string') {
                        this.$message(rcvData);
                        return;
                    }
                    callBack && callBack(rcvData);
                })
                .catch((err) => {
                    catchCallback && catchCallback(); //失败回调
                    this.$exloaded1x();
                    let options = {
                        title: '消息提示',
                        content: errContent + '接口请求失败！',
                        detail: `详细内容：${err.errorMessage || err}`
                    };
                    this.$popupMessageWindow(options);
                });
        }
    }
};
</script>

<style lang="less" scoped>
.gold-approval-mask {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}
.scrollbar-custom {
    --scrollbar-width: 4px;
    --scrollbar-height: 4px;
    --scrollbar-thumb-color: #5c6f92;
    &::-webkit-scrollbar {
        width: var(--scrollbar-width);
        height: var(--scrollbar-height);
    }
    &::-webkit-scrollbar-thumb {
        border-radius: 10px;
        box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
        background: var(--scrollbar-thumb-color);
    }
    &::-webkit-scrollbar-track {
        /* 滚动条里面轨道 */
        box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
        border-radius: 10px;
        background: transparent;
    }
    &::-webkit-scrollbar-corner {
        background: rgba(0, 0, 0, 0);
    }
}

@width-1x: 520px;
@height-1x: 349px;
@bg-1x: url('~@/img/goldApproval/gold-approval-bg.png');
@bg-2x: url('~@/img/goldApproval/gold-approval-bg-2x.png');
@header-height-1x: 24px;
@close-top-1x: 45px;
@close-size-1x: 20px;
@btn-width: 360px;

.gold-approval-container {
    --width: @width-1x;
    --height: @height-1x;
    --bg: @bg-1x;
    --header-height: @header-height-1x;
    --close-top: @close-top-1x;
    --close-size: @close-size-1x;
    --btn-width: @btn-width;

    width: var(--width);
    height: var(--height);
    background-image: var(--bg);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    padding-top: 5px;
    display: flex;
    flex-direction: column;
    position: relative;
    transition: all 0.3s ease-in-out;
    &.gold-approval-container-2x {
        --width: calc(@width-1x * 1.2);
        --height: calc(@height-1x * 1.2);
        --bg: @bg-2x;
        --header-height: calc(@header-height-1x * 1.5);
        --close-top: calc(@close-top-1x * 1.2);
        --close-size: calc(@close-size-1x * 1.2);
        --btn-width: 100%;
    }

    .gold-approval-header {
        display: flex;
        align-items: center;
        justify-content: center;
        height: var(--header-height);
        flex: none;
        .gold-approval-title {
            font-weight: 500;
            font-size: 16px;
            color: #ffffff;
            line-height: 24px;
            letter-spacing: 1px;
        }
    }
    .gold-approval-close {
        position: absolute;
        right: var(--close-size);
        top: var(--close-top);
        font-size: var(--close-size);
        font-weight: 500;
        cursor: pointer;
        color: #acc2d3;
        &:hover {
            color: #f74041;
        }
    }
    .gold-approval-main {
        min-height: 0;
        flex: 1;
        padding: 40px var(--close-size) 50px;
        &-content {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 24px;
            padding: 0 48px;

            .gold-approval-main-layout {
                width: 100%;
                display: flex;
                gap: 20px;
                min-height: 0;
                flex: 1;

                .gold-approval-left-panel {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    gap: 15px;
                }
            }
            .gold-approval-steps {
                width: 44%;
                display: flex;
                align-items: center;
                margin-bottom: 10px;

                .gold-approval-step-wrapper {
                    display: flex;
                    align-items: center;
                    flex: 1;

                    &:last-child {
                        flex: 0;
                    }

                    .gold-approval-step {
                        width: 9px;
                        height: 9px;
                        border-radius: 50%;
                        background: rgba(0, 149, 255, 0.5);
                        flex-shrink: 0;

                        &.process {
                            background: rgba(1, 230, 254, 1);
                            position: relative;
                            &::before {
                                content: '';
                                position: absolute;
                                width: 21px;
                                height: 21px;
                                border-radius: 50%;
                                background: rgba(2, 244, 255, 0.2);
                                left: 50%;
                                top: 50%;
                                transform: translate(-50%, -50%);
                                animation: ripple 1.5s ease-in-out infinite;
                            }

                            @keyframes ripple {
                                0% {
                                    width: 9px;
                                    height: 9px;
                                    opacity: 1;
                                }
                                70% {
                                    width: 18px;
                                    height: 18px;
                                    opacity: 0.9;
                                }
                                100% {
                                    width: 21px;
                                    height: 21px;
                                    opacity: 0.2;
                                }
                            }
                        }
                        &.finish {
                            background: rgba(1, 230, 254, 1);
                        }
                    }

                    .gold-approval-step-line {
                        height: 1px;
                        flex: 1;
                        background: rgba(0, 149, 255, 0.2);

                        &.process {
                            background: rgba(1, 230, 254, 0.2);
                        }

                        &.finish {
                            background: rgba(1, 230, 254, 0.8);
                        }
                    }
                }
            }
            .gold-approval-approver-select {
                align-self: flex-start;
                width: 100%;
                /deep/&.el-select {
                    height: 40px;
                    background: rgba(0, 40, 85, 0.6);
                    border-radius: 4px;
                    .el-input__inner {
                        background: transparent;
                        font-weight: 500;
                        font-size: 14px;
                        color: #ffffff;
                        line-height: 16px;
                        border: 1px solid rgba(18, 139, 207, 0.6);
                        &::placeholder {
                            font-weight: 400;
                            font-size: 14px;
                            color: rgba(255, 255, 255, 0.45);
                            line-height: 16px;
                        }
                        &:focus {
                            box-shadow: 0px 4px 20px 0px #004e9e;
                        }
                    }
                    .el-input__prefix {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        left: 7px;
                        .gold-approval-user-icon,
                        .gold-approval-company-icon {
                            width: 16px;
                            height: 16px;
                        }
                    }
                    .el-input__suffix-inner {
                        .el-select__caret {
                            color: rgba(0, 146, 250, 1);
                        }
                    }
                }
            }
            .gold-approval-apply-reason {
                width: 100%;
                min-height: 0;
                flex: 1;
                background: rgba(0, 40, 85, 0.6);
                border-radius: 4px;
                font-weight: 500;
                font-size: 14px;
                color: #ffffff;
                line-height: 16px;
                border: 1px solid rgba(18, 139, 207, 0.6);
                padding: 16px;
                padding-right: 12px;
                display: flex;
                flex-direction: column;
                flex-wrap: wrap;
                &-item {
                    display: grid;
                    grid-template-columns: auto 1fr;
                    align-items: center;
                    gap: 10px;
                }
                &-select {
                    align-self: flex-start;
                    width: 100%;
                    /deep/&.el-select {
                        height: 40px;
                        background: rgba(0, 40, 85, 0.6);
                        border-radius: 4px;
                        .el-input__inner {
                            background: transparent;
                            font-weight: 500;
                            font-size: 14px;
                            color: #ffffff;
                            line-height: 16px;
                            border: 1px solid rgba(18, 139, 207, 0.6);
                            &::placeholder {
                                font-weight: 400;
                                font-size: 14px;
                                color: rgba(255, 255, 255, 0.45);
                                line-height: 16px;
                            }
                            &:focus {
                                box-shadow: 0px 4px 20px 0px #004e9e;
                            }
                        }
                    }
                }
                &-input {
                    width: 100%;
                    height: 40px;
                    background: rgba(0, 40, 85, 0.6);
                    border-radius: 4px;

                    /deep/.el-input__inner {
                        background: transparent;
                        font-weight: 500;
                        font-size: 14px;
                        color: #ffffff;
                        line-height: 16px;
                        border: 1px solid rgba(18, 139, 207, 0.6);
                        &::placeholder {
                            font-weight: 400;
                            font-size: 14px;
                            color: rgba(255, 255, 255, 0.45);
                            line-height: 16px;
                        }
                        &:focus {
                            box-shadow: 0px 4px 20px 0px #004e9e;
                        }
                    }
                }
            }
            .gold-approval-verify-code {
                width: 360px;
                height: 40px;
                background: rgba(0, 40, 85, 0.6);
                border-radius: 4px;

                /deep/.el-input__inner {
                    background: transparent;
                    font-weight: 500;
                    font-size: 14px;
                    color: #ffffff;
                    line-height: 16px;
                    border: 1px solid rgba(18, 139, 207, 0.6);
                    &::placeholder {
                        font-weight: 400;
                        font-size: 14px;
                        color: rgba(255, 255, 255, 0.45);
                        line-height: 16px;
                    }
                    &:focus {
                        box-shadow: 0px 4px 20px 0px #004e9e;
                    }
                }
                /deep/&:has(.gold-approval-code__msg) .el-input__inner {
                    padding-right: 132px;
                }
                /deep/&:has(.gold-approval-code__msg.resend) .el-input__inner {
                    padding-right: 76px;
                }

                /deep/.el-input__prefix {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    left: 7px;
                    .gold-approval-edit-icon {
                        width: 16px;
                        height: 16px;
                    }
                }
                /deep/.el-input__suffix:has(.gold-approval-code__msg) {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    right: 10px;
                    .el-input__suffix-inner {
                        display: flex;
                    }
                    .gold-approval-code__msg {
                        font-weight: 400;
                        font-size: 14px;
                        color: #d1e4ff;
                        cursor: default;
                        &.resend {
                            color: #00ceff;
                            cursor: pointer;
                            &:hover {
                                opacity: 0.8;
                            }
                        }
                    }
                }
                &.select-reason-error {
                    /deep/.el-input__inner {
                        border: 1px solid #ff4949;
                    }
                    &::before {
                        content: '* 理由长度需大于8个字符';
                        position: absolute;
                        bottom: -20px;
                        left: 0;
                        font-size: 12px;
                        color: #ff4949;
                    }
                }
            }
            .gold-approval-verify-result {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 6px;
                &:has(.el-icon-success) {
                    .gold-approval-verify-result-icon,
                    .gold-approval-verify-result-text {
                        color: #36da54;
                    }
                }
                &:has(.el-icon-warning) {
                    .gold-approval-verify-result-icon,
                    .gold-approval-verify-result-text {
                        color: #ff3030;
                    }
                }
                .gold-approval-verify-result-icon,
                .gold-approval-verify-result-text {
                    font-weight: 400;
                    font-size: 14px;
                }
            }
            .gold-approval-submit-btn {
                /deep/&.el-button {
                    transition: all 0.3s ease-in-out;
                    width: var(--btn-width);
                    height: 40px;
                    border-radius: 4px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-top: auto;
                    &--primary {
                        background-color: #0095ff;
                        border-color: #0095ff;
                        &:hover,
                        &:focus {
                            background-color: darken(#0095ff, 10%);
                            border-color: darken(#0095ff, 10%);
                        }

                        &:active {
                            background-color: darken(#0095ff, 20%);
                            border-color: darken(#0095ff, 20%);
                        }

                        &.is-disabled,
                        &.is-disabled:hover,
                        &.is-disabled:focus,
                        &.is-disabled:active {
                            background-color: #0095ff;
                            border-color: #0095ff;
                            opacity: 0.4;
                        }
                    }
                }
            }
        }
    }
}
</style>
<style lang="less">
.gold-approval-select-popper {
    background: rgba(0, 10, 38, 0.98);
    box-shadow: 0px 4px 20px 0px #004e9e;
    border-radius: 4px;
    border: 1px solid rgba(18, 139, 207, 0.6);
    max-width: 720px;
    overflow: auto;
    &[x-placement^='bottom'] .popper__arrow {
        border-bottom-color: rgba(18, 139, 207, 0.6);
        &::after {
            border-bottom-color: rgba(0, 10, 38, 0.98);
        }
    }
    .el-select-dropdown__item {
        font-weight: 400;
        font-size: 14px;
        color: #d1e4ff;
        &:last-child {
            margin-bottom: 10px;
        }
        &.selected:not(.is-disabled) {
            font-weight: 400;
            font-size: 14px;
            color: #00ceff;
            background-color: rgba(0, 149, 255, 0.15);
        }
        &.hover {
            background-color: rgba(0, 149, 255, 0.15);
        }
        &:hover {
            background-color: rgba(0, 149, 255, 0.15);
        }
    }
    // 滚动条调成透明
    .el-scrollbar__wrap {
        &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        &::-webkit-scrollbar-track {
            box-shadow: inset 0 0 5px transparent;
            border-radius: 10px;
            background: transparent;
        }
    }
}
</style>
