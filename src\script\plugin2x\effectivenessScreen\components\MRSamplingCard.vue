<template>
    <SectionCard
        class="left-section-card"
        :title="level === 'province' ? '全省MR采样数量' : '全市MR采样数量'"
        titleBg="title-bg1"
    >
        <div class="mr-collect-container">
            <div class="mr-collect-left">
                <div class="pie-chart-container">
                    <div ref="pieChart" class="pie-chart"></div>
                    <div ref="pieCenterContent" class="pie-center-content">
                        <div
                            class="percent"
                            :style="isUp ? '--percent-color: #00eb98' : '--percent-color: #FF4545'"
                        >
                            <span class="percent-icon">{{ isUp ? '▲' : '▼' }} </span>
                            <span class="percent-value">{{ mrData.growthPercentage }}%</span>
                        </div>
                        <div class="number" :title="mrData.yesterdayTotalCount">
                            {{ mrData.yesterdayTotalCountToFixed }}<span>亿</span>
                        </div>
                        <div class="label">采样总数</div>
                    </div>
                </div>
            </div>
            <div class="mr-collect-right">
                <div class="mr-collect-right-item">
                    <div class="network-type-4G"></div>
                    <div class="ratio-item ratio-item-4G">
                        <span class="ratio-label">采样点占比</span>
                        <span class="ratio-value">{{ mrData.yesterday4GPercentage }}%</span>
                    </div>
                </div>
                <div class="mr-collect-right-item">
                    <div class="network-type-5G"></div>
                    <div class="ratio-item ratio-item-5G">
                        <span class="ratio-label">采样点占比</span>
                        <span class="ratio-value">{{ mrData.yesterday5GPercentage }}%</span>
                    </div>
                </div>
            </div>
        </div>
    </SectionCard>
</template>

<script>
import * as echarts from 'echarts';
import SectionCard from './SectionCard.vue';
import commonMixins from '@/script/mixins/commonMixins.js';

export default {
    name: 'MRSamplingCard',
    props: {
        level: {
            type: String,
            default: 'province'
        },
        cityId: {
            type: Number,
            default: null
        }
    },
    mixins: [commonMixins],
    components: {
        SectionCard
    },
    data() {
        return {
            pieChart: null,
            resizeObserver: null,
            mrData: {
                yesterdayTotalCount: '-',
                yesterday4GTotalCount: '-',
                yesterday5GTotalCount: '-',
                yesterday4GPercentage: '-',
                yesterday5GPercentage: '-',
                growthPercentage: '-',
                yesterdayTotalCountToFixed: '-'
            }
        };
    },
    computed: {
        isUp() {
            return this.mrData.growthPercentage > 0;
        }
    },
    watch: {
        cityId: {
            handler(newVal) {
                this.fetchData();
            }
        }
    },
    mounted() {
        this.initChart();
        this.fetchData();
    },
    methods: {
        fetchData() {
            this.getPost('post', 'mrData', { cityId: this.cityId }, 'MR采样数量', (data) => {
                // 处理空数据情况
                if (!data || !data.length || !data[0] || Object.keys(data[0]).length === 0) {
                    return;
                }
                this.mrData = {
                    yesterdayTotalCount: data[0].yesterdayTotalCount,
                    yesterday4GTotalCount: data[0].yesterday4GTotalCount,
                    yesterday5GTotalCount: data[0].yesterday5GTotalCount,
                    yesterday4GPercentage: data[0].yesterday4GPercentage,
                    yesterday5GPercentage: data[0].yesterday5GPercentage,
                    growthPercentage: data[0].growthPercentage,
                    yesterdayTotalCountToFixed: (data[0].yesterdayTotalCount / 1e8).toFixed(2)
                };
                this.pieChart.setOption({
                    series: [
                        {
                            data: [
                                {
                                    value: this.mrData.yesterday4GPercentage,
                                    name: '4G',
                                    count: this.mrData.yesterday4GTotalCount
                                },
                                {
                                    value: this.mrData.yesterday5GPercentage,
                                    name: '5G',
                                    count: this.mrData.yesterday5GTotalCount
                                }
                            ]
                        }
                    ]
                });
            });
        },
        initChart() {
            if (this.$refs.pieChart) {
                this.pieChart = echarts.init(this.$refs.pieChart);
                const option = {
                    color: ['#008BFF', '#19EBFF'],
                    graphic: [
                        {
                            type: 'image',
                            left: 'center', //调整图片位置
                            top: 'center', //调整图片位置
                            bounding: 'all',
                            rotation: 0, //旋转
                            scale: [1.0, 1.0], //缩放
                            //设置图片样式
                            style: {
                                image: require('../../../../img/effectivenessScreen/pie-bg.png'),
                                width: this.$refs.pieChart.clientWidth - 20,
                                height: this.$refs.pieChart.clientWidth - 20,
                                opacity: 1
                            }
                        }
                    ],
                    tooltip: {
                        trigger: 'item',
                        confine: true,
                        backgroundColor: '#09437B',
                        borderColor: '#2D72B6',
                        borderWidth: 1,
                        padding: [5, 10],
                        textStyle: {
                            color: '#fff',
                            fontSize: 12
                        },
                        formatter: (params) => {
                            return `<strong>${params.data.name}</strong> <br/> 采样点数量：${params.data.count} <br/> 采样点占比：${params.data.value}%`;
                        }
                    },
                    series: [
                        {
                            type: 'pie',
                            radius: ['86%', '100%'],
                            center: ['50%', '50%'],
                            startAngle: 90,
                            padAngle: 2,
                            data: [],
                            label: {
                                show: false
                            },
                            emphasis: {
                                scale: false
                            }
                        }
                    ]
                };
                this.pieChart.setOption(option);
                this.resizeObserver = new ResizeObserver(() => {
                    this.pieChart.resize();
                });
                this.resizeObserver.observe(this.$refs.pieChart);
            }
        }
    },
    beforeDestroy() {
        if (this.resizeObserver) {
            this.resizeObserver.disconnect();
            this.resizeObserver = null;
        }
        if (this.pieChart) {
            this.pieChart.dispose();
            this.pieChart = null;
        }
    }
};
</script>

<style lang="less" scoped>
.mr-collect-container {
    width: 100%;
    height: 100%;
    display: grid;
    gap: 0.2778rem;
    grid-template-columns: 1fr 1.4fr;
    padding-top: 0.8889rem;

    .mr-collect-left {
        position: relative;

        .pie-chart-container {
            width: 100%;
            height: 100%;
            position: relative;

            .pie-chart {
                width: 100%;
                height: 100%;
            }

            .pie-center-content {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                text-align: center;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                gap: 0.2222rem;

                .percent {
                    --percent-color: #00eb98;
                    .percent-icon {
                        font-size: 0.4444rem;
                        line-height: 0.4444rem;
                        color: var(--percent-color);
                    }
                    .percent-value {
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 500;
                        font-size: 0.6667rem;
                        line-height: 0.6667rem;
                        color: var(--percent-color);
                    }
                }

                .number {
                    font-family: YouSheBiaoTiHei;
                    font-size: 1.2222rem;
                    line-height: 1.2222rem;
                    color: #ffffff;
                    text-shadow: 0px 0px 0.5rem rgba(1, 220, 255, 0.59);
                    white-space: nowrap;
                    span {
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 500;
                        font-size: 0.5556rem;
                        line-height: 0.5556rem;
                        color: #ffffff;
                        text-shadow: 0px 0px 0.5rem rgba(1, 220, 255, 0.59);
                    }
                }

                .label {
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 0.6667rem;
                    color: #94c3f2;
                }
            }
        }
    }

    .mr-collect-right {
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: 1.3333rem;

        .mr-collect-right-item {
            display: flex;
            align-items: center;
            gap: 0.5556rem;

            .network-type-4G {
                width: 2.2222rem;
                height: 2.2222rem;
                background-image: url('../../../../img/effectivenessScreen/icon-4G.png');
                background-size: 100% 100%;
                background-repeat: no-repeat;
            }

            .network-type-5G {
                width: 2.2222rem;
                height: 2.2222rem;
                background-image: url('../../../../img/effectivenessScreen/icon-5G.png');
                background-size: 100% 100%;
                background-repeat: no-repeat;
            }

            .ratio-item {
                flex: 1;
                height: 2.2222rem;
                display: flex;
                align-items: center;
                justify-content: space-between;
                background-image: url('../../../../img/effectivenessScreen/mr-legend-bg.png');
                background-size: 100% 100%;
                background-repeat: no-repeat;
                padding: 0 1.3333rem;

                &.ratio-item-4G {
                    .ratio-label {
                        &::before {
                            background: #008bff;
                        }
                    }
                }
                &.ratio-item-5G {
                    .ratio-label {
                        &::before {
                            background: #19ebff;
                        }
                    }
                }

                .ratio-label {
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 0.7778rem;
                    color: #ffffff;
                    position: relative;
                    &::before {
                        content: '';
                        position: absolute;
                        left: -0.6667rem;
                        top: 50%;
                        transform: translate(-50%, -50%);
                        display: inline-block;
                        width: 0.5556rem;
                        height: 0.5556rem;
                        background: #008bff;
                    }
                }

                .ratio-value {
                    background: linear-gradient(180deg, #ffffff 0%, #9be5ff 50%, #0dcaf5 80%);
                    -webkit-background-clip: text;
                    background-clip: text;
                    color: transparent;
                    font-family: YouSheBiaoTiHei;
                    font-size: 1.1111rem;
                }
            }
        }
    }
}
</style>
